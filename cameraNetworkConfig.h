#pragma once

#include "cameraApiExport.h"
#include <string>

namespace percipio {
    class CAMERA_API_EXPORT CameraNetworkConfig {
    public:
        static ResultCode SetIpAddress(const CameraInfo& camera_info, const std::string& ip);

        static ResultCode SetIpAddress(const CameraInfo& camera_info, const std::string& ip, 
                                     const std::string& mask, const std::string& gateway);

        /**
         * @brief Set IP address for a camera using MAC address
         * @param mac MAC address of the camera
         * @param ip IP address to set
         * @return ResultCode indicating success or failure
         */
        static ResultCode SetIpAddress(const std::string& mac, const std::string& ip);

        /**
         * @brief Set IP address with subnet mask and gateway for a camera using MAC address
         * @param mac MAC address of the camera
         * @param ip IP address to set
         * @param mask Subnet mask
         * @param gateway Gateway address
         * @return ResultCode indicating success or failure
         */
        static ResultCode SetIpAddress(const std::string& mac, const std::string& ip, 
                                     const std::string& mask, const std::string& gateway);

        /**
         * @brief Enable dynamic IP (DHCP) for a camera using CameraInfo
         * @param camera_info Camera information structure
         * @return ResultCode indicating success or failure
         */
        static ResultCode SetIpToDynamic(const CameraInfo& camera_info);

        /**
         * @brief Enable dynamic IP (DHCP) for a camera using MAC address
         * @param mac MAC address of the camera
         * @return ResultCode indicating success or failure
         */
        static ResultCode SetIpToDynamic(const std::string& mac);

    private:
        // Private constructor to prevent instantiation of utility class
        CameraNetworkConfig() = delete;
        ~CameraNetworkConfig() = delete;
        CameraNetworkConfig(const CameraNetworkConfig&) = delete;
        CameraNetworkConfig& operator=(const CameraNetworkConfig&) = delete;
    };
}
