#pragma once

#include "cameraApiExport.h"
#include <memory>
#include <functional>
#include <string>
#include <vector>

// 版本信息
// 本代码
// vcamera
// tycam SDK 版本
namespace percipio {
	// SetIp
	static ResultCode SetIpAddress(const CameraInfo& camera_info, std::string ip);
	static ResultCode SetIpAddress(const CameraInfo& camera_info, std::string ip, std::string mask, std::string gateway);
	static ResultCode SetIpAddress(std::string mac, std::string ip);
	static ResultCode SetIpAddress(std::string mac, std::string ip, std::string mask, std::string gateway);
	// SetIpToDynamic
	static ResultCode SetIpToDynamic(const CameraInfo& camera_info);
	static ResultCode SetIpToDynamic(std::string mac);


	
	class CAMERA_API_EXPORT Camera {
	public:
		static std::vector<CameraInfo> DiscoverCameras();

		/*static Camera GetCameraBySn(std::string sn);
		static Camera GetCameraByIp(std::string ip_address);
		static Camera GetCameraByCameraInfo(const CameraInfo& info);*/

		Camera();

		// std::string GetLastErrorMessage();
		// TODO: ResultCode 修改，不是只有枚举值，应该是包含 desc 的结构体
		ResultCode ConnectBySn(std::string, std::string session_key = "");
		ResultCode ConnectByIpAddress(std::string, std::string session_key = "");
		ResultCode ConnectByCameraInfo(CameraInfo, std::string session_key="");
		ResultCode Disconnect();
		ResultCode GetCameraStatus(CameraStatus& camera_status);


		// 这两个概念尽量规避
		// component
		// sensor
		//ResultCode GetComponent(SensorType sensor_type);
		CameraFeature GetFeature(ComponentType, "Width");
		/*{
			CameraFeature feat = GetFeature();
			feat.GetValue();
			feat.SetValue(100);
		}*/
		ResultCode FireSoftwareTrigger();

		// TODO 讨论
		ResultCode GetImageModes(ComponentType sensor_type, std::vector<ImageMode>& resolutions);
		ResultCode GetCurrentImageMode(ComponentType sensor_type, ImageMode& image_mode);
		ResultCode SetImageMode(ComponentType sensor_type, const ImageMode& mode);
		ResultCode SetUndistortionEnabled(SensorType sensor_type, bool enable);

		ResultCode GetFeatures(std::vector<CameraFeature>& features);

		// 以下是否放到一个 UserSet 管理类中？
		ResultCode GetAllUserSets(std::vector<UserSet>& user_sets);
		ResultCode GetCurrentUserSet(UserSet& user_set);
		ResultCode LoadUserSet(std::string user_set_name);
		ResultCode GetDefaultUserSet(UserSet& user_set_name);
		ResultCode SetDefaultUserSet(std::string user_set_name);
		ResultCode SaveFeaturesToUserSet(std::string user_set_name);

		using FrameSetCallback = std::function<void(const FrameSet& frame_set)>;
		using DeviceEventCallback = std::function<void(DeviceEventCode event_code, int error_code)>;
		using FeaturesChangedCallback = std::function<void(const std::vector<CameraFeature>&)>;

		void RegisterFrameSetCallback(FrameSetCallback callback);
		void RegisterDeviceEventCallback(DeviceEventCallback callback);
		void RegisterFeaturesChangedCallback(FeaturesChangedCallback callback);
	
	private:
		std::shared_ptr<class CameraImpl> camera_impl_;
	};
}
